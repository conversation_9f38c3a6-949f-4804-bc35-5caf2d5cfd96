#!/usr/bin/env python3
"""
Simple Mixpanel Test Script - Debug version

This script tests individual components to isolate where the hang is occurring.
"""

import os
import sys
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add dependencies to path
sys.path.append(os.path.join(os.path.dirname(__file__), "dags", "dependencies"))


def test_imports():
    """Test if all imports work correctly."""
    print("🔍 Testing imports...")

    try:
        from mixpanel.mixpanel_client import MixpanelClient

        print("   ✅ MixpanelClient imported successfully")
    except Exception as e:
        print(f"   ❌ MixpanelClient import failed: {e}")
        return False

    try:
        from mixpanel.mixpanel_tasks import fetch_mixpanel_analytics_task

        print("   ✅ mixpanel_tasks imported successfully")
    except Exception as e:
        print(f"   ❌ mixpanel_tasks import failed: {e}")
        return False

    return True


def test_environment_variables():
    """Test if all required environment variables are set."""
    print("\n🔍 Testing environment variables...")

    required_vars = [
        "MIXPANEL_PROJECT_ID",
        "MIXPANEL_SERVICE_USERNAME",
        "MIXPANEL_SERVICE_SECRET",
        "MIXPANEL_WORKSPACE_ID",
        "SUPABASE_URL",
        "SUPABASE_SERVICE_ROLE_KEY",
    ]

    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"   ✅ {var}: {'*' * min(len(value), 10)}...")
        else:
            print(f"   ❌ {var}: Not set")
            missing_vars.append(var)

    return len(missing_vars) == 0


def test_mixpanel_client_init():
    """Test if Mixpanel client can be initialized."""
    print("\n🔍 Testing Mixpanel client initialization...")

    try:
        from mixpanel.mixpanel_client import MixpanelClient

        client = MixpanelClient(
            project_id=os.getenv("MIXPANEL_PROJECT_ID"),
            service_account_username=os.getenv("MIXPANEL_SERVICE_USERNAME"),
            service_account_secret=os.getenv("MIXPANEL_SERVICE_SECRET"),
            workspace_id=os.getenv("MIXPANEL_WORKSPACE_ID"),
            respect_rate_limits=False,  # Disable for testing
        )

        print("   ✅ MixpanelClient initialized successfully")
        print(f"   📊 Project ID: {client.project_id}")
        print(f"   🏢 Workspace ID: {client.workspace_id}")
        return client

    except Exception as e:
        print(f"   ❌ MixpanelClient initialization failed: {e}")
        return None


def test_simple_api_call(client):
    """Test a simple API call to Mixpanel using the optimized method."""
    print("\n🔍 Testing Mixpanel optimized API call...")

    if not client:
        print("   ❌ No client available for testing")
        return False

    try:
        # Use a date that's definitely in the past
        test_date = "2024-12-01"  # Use a known past date

        print(f"   📅 Testing with date: {test_date}")
        print("   🔄 Making optimized API call (this might take a moment)...")

        # Test with a timeout
        import signal

        def timeout_handler(signum, frame):
            raise TimeoutError("API call timed out")

        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(60)  # 60 second timeout for optimized call

        try:
            # Use the same method the DAG uses
            result = client.get_all_analytics_data_optimized(
                from_date=test_date,
                to_date=test_date,
                include_user_data=False,  # Skip user data for faster test
                include_retention=False,  # Skip retention for faster test
            )
            signal.alarm(0)  # Cancel timeout

            if result and result.get("success"):
                print("   ✅ API call successful")
                print(
                    f"   📊 API calls used: {result.get('api_calls_used', 'unknown')}"
                )
                data = result.get("data", {})
                print(f"   📈 Data types: {list(data.keys())}")

                # Show some sample data
                for data_type, data_content in data.items():
                    if data_content:
                        print(f"      🔍 {data_type}: {type(data_content).__name__}")

                return True
            else:
                print("   ❌ API call returned no data or failed")
                if result:
                    print(f"      Error: {result.get('error', 'Unknown error')}")
                return False

        except TimeoutError:
            print("   ❌ API call timed out after 60 seconds")
            return False
        finally:
            signal.alarm(0)  # Make sure to cancel timeout

    except Exception as e:
        print(f"   ❌ API call failed: {e}")
        return False


def test_supabase_connection():
    """Test Supabase connection."""
    print("\n🔍 Testing Supabase connection...")

    try:
        from supabase import create_client, Client

        url = os.getenv("SUPABASE_URL")
        key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")

        supabase: Client = create_client(url, key)

        # Test with a simple query
        result = supabase.table("user_profiles").select("*").limit(1).execute()

        print("   ✅ Supabase connection successful")
        print(f"   📊 Test query returned {len(result.data)} records")
        return True

    except Exception as e:
        print(f"   ❌ Supabase connection failed: {e}")
        return False


def main():
    """Run all tests in sequence."""
    print("🚀 Starting Simple Mixpanel Test")
    print("=" * 50)

    # Test 1: Imports
    if not test_imports():
        print("\n❌ Import test failed - stopping here")
        return

    # Test 2: Environment variables
    if not test_environment_variables():
        print("\n❌ Environment variables test failed - stopping here")
        return

    # Test 3: Mixpanel client initialization
    client = test_mixpanel_client_init()
    if not client:
        print("\n❌ Mixpanel client initialization failed - stopping here")
        return

    # Test 4: Simple API call
    api_success = test_simple_api_call(client)

    # Test 5: Supabase connection
    supabase_success = test_supabase_connection()

    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    print(f"✅ Imports: PASSED")
    print(f"✅ Environment Variables: PASSED")
    print(f"✅ Mixpanel Client Init: PASSED")
    print(
        f"{'✅' if api_success else '❌'} Mixpanel API Call: {'PASSED' if api_success else 'FAILED'}"
    )
    print(
        f"{'✅' if supabase_success else '❌'} Supabase Connection: {'PASSED' if supabase_success else 'FAILED'}"
    )

    if api_success and supabase_success:
        print("\n🎉 All tests passed! The full DAG should work.")
    else:
        print("\n⚠️ Some tests failed. Check the errors above.")


if __name__ == "__main__":
    main()
